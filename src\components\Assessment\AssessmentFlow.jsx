import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import apiService from '../../services/apiService';
import AssessmentForm from './AssessmentForm';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';

const AssessmentFlow = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const isDebugMode = searchParams.get('debug') === 'true';

  const [currentStep, setCurrentStep] = useState(1);
  const [assessmentResults, setAssessmentResults] = useState({
    via: null,
    riasec: null,
    bigFive: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tokenBalance, setTokenBalance] = useState(null);
  const [isCheckingToken, setIsCheckingToken] = useState(false);

  const assessments = [
    { key: 'via', data: viaQuestions, title: 'VIA Character Strengths' },
    { key: 'riasec', data: riasecQuestions, title: 'RIASEC Holland Codes' },
    { key: 'bigFive', data: bigFiveQuestions, title: 'Big Five Personality' }
  ];

  // Debug function to auto-fill assessments
  const generateDebugScores = () => {
    const debugScores = {
      via: {
        creativity: 85,
        curiosity: 78,
        judgment: 70,
        loveOfLearning: 82,
        perspective: 60,
        bravery: 55,
        perseverance: 68,
        honesty: 73,
        zest: 66,
        love: 80,
        kindness: 75,
        socialIntelligence: 65,
        teamwork: 60,
        fairness: 70,
        leadership: 67,
        forgiveness: 58,
        humility: 62,
        prudence: 69,
        selfRegulation: 61,
        appreciationOfBeauty: 50,
        gratitude: 72,
        hope: 77,
        humor: 65,
        spirituality: 55
      },
      riasec: {
        realistic: 75,
        investigative: 85,
        artistic: 60,
        social: 50,
        enterprising: 70,
        conventional: 55
      },
      bigFive: {
        openness: 80,
        conscientiousness: 65,
        extraversion: 55,
        agreeableness: 45,
        neuroticism: 30
      }
    };
    return debugScores;
  };

  const currentAssessment = assessments[currentStep - 1];

  // Auto-fill assessments in debug mode
  useEffect(() => {
    if (isDebugMode && import.meta.env.DEV) {
      const debugScores = generateDebugScores();
      setAssessmentResults(debugScores);
      setCurrentStep(3); // Go to last assessment
    }
  }, [isDebugMode]);

  // Fetch token balance on component mount
  useEffect(() => {
    const fetchTokenBalance = async () => {
      try {
        const response = await apiService.getTokenBalance();
        if (response.success) {
          setTokenBalance(response.data.token_balance);
        }
      } catch (err) {
        setTokenBalance(0); // Set to 0 if failed to fetch
      }
    };

    fetchTokenBalance();
  }, []);

  // Function to check token balance before submission
  const checkTokenBalance = async () => {
    setIsCheckingToken(true);
    try {
      const response = await apiService.getTokenBalance();
      if (response.success) {
        const balance = response.data.token_balance;
        setTokenBalance(balance);
        return balance;
      }
      return 0;
    } catch (err) {
      return 0;
    } finally {
      setIsCheckingToken(false);
    }
  };

  const handleAssessmentComplete = (scores) => {
    console.log('🔍 DEBUG - handleAssessmentComplete called with scores:', scores);
    console.log('🔍 DEBUG - Current assessment key:', currentAssessment.key);
    console.log('🔍 DEBUG - Current step:', currentStep);
    console.log('🔍 DEBUG - Previous assessment results:', assessmentResults);
    console.log('🔍 DEBUG - Previous assessment results keys:', Object.keys(assessmentResults));
    console.log('🔍 DEBUG - Previous assessment results values:', Object.values(assessmentResults));

    const newResults = {
      ...assessmentResults,
      [currentAssessment.key]: scores
    };

    console.log('🔍 DEBUG - New assessment results:', newResults);
    console.log('🔍 DEBUG - New assessment results keys:', Object.keys(newResults));
    console.log('🔍 DEBUG - New assessment results values:', Object.values(newResults));
    setAssessmentResults(newResults);

    // Only move to next assessment if not the last one
    if (currentStep < assessments.length) {
      console.log('🔍 DEBUG - Moving to next assessment, new step will be:', currentStep + 1);
      setCurrentStep(prev => prev + 1);
    } else {
      console.log('🔍 DEBUG - This is the last assessment, not auto-advancing');
    }
    // Don't auto-advance on last assessment, let user explicitly click submit
  };

  const handleNext = () => {
    if (currentStep < assessments.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const transformScoresForAPI = (resultsToTransform = assessmentResults) => {
    console.log('🔍 DEBUG - transformScoresForAPI called');
    console.log('🔍 DEBUG - Results to transform:', resultsToTransform);

    const { via, riasec, bigFive } = resultsToTransform;
    console.log('🔍 DEBUG - Destructured scores - VIA:', via, 'RIASEC:', riasec, 'BigFive:', bigFive);

    // Transform VIA scores to match API format with new 6-category structure
    // Note: API still expects the original 24 individual scores, so we need to map from 6 categories back to 24
    const viaIs = {
      // Wisdom and Knowledge (5 strengths)
      creativity: (via?.wisdomAndKnowledge || 0),
      curiosity: (via?.wisdomAndKnowledge || 0),
      judgment: (via?.wisdomAndKnowledge || 0),
      loveOfLearning: (via?.wisdomAndKnowledge || 0),
      perspective: (via?.wisdomAndKnowledge || 0),
      // Courage (4 strengths)
      bravery: (via?.courage || 0),
      perseverance: (via?.courage || 0),
      honesty: (via?.courage || 0),
      zest: (via?.courage || 0),
      // Humanity (3 strengths)
      love: (via?.humanity || 0),
      kindness: (via?.humanity || 0),
      socialIntelligence: (via?.humanity || 0),
      // Justice (3 strengths)
      teamwork: (via?.justice || 0),
      fairness: (via?.justice || 0),
      leadership: (via?.justice || 0),
      // Temperance (4 strengths)
      forgiveness: (via?.temperance || 0),
      humility: (via?.temperance || 0),
      prudence: (via?.temperance || 0),
      selfRegulation: (via?.temperance || 0),
      // Transcendence (5 strengths)
      appreciationOfBeauty: (via?.transcendence || 0),
      gratitude: (via?.transcendence || 0),
      hope: (via?.transcendence || 0),
      humor: (via?.transcendence || 0),
      spirituality: (via?.transcendence || 0)
    };

    // Transform RIASEC scores to match API format
    const riasecScores = {
      realistic: (riasec?.realistic || 0),
      investigative: (riasec?.investigative || 0),
      artistic: (riasec?.artistic || 0),
      social: (riasec?.social || 0),
      enterprising: (riasec?.enterprising || 0),
      conventional: (riasec?.conventional || 0)
    };

    // Transform Big Five scores to match API format (OCEAN)
    const ocean = {
      openness: (bigFive?.openness || 0),
      conscientiousness: (bigFive?.conscientiousness || 0),
      extraversion: (bigFive?.extraversion || 0),
      agreeableness: (bigFive?.agreeableness || 0),
      neuroticism: (bigFive?.neuroticism || 0)
    };

    const transformedData = {
      riasec: riasecScores,
      ocean: ocean,
      viaIs: viaIs
    };

    console.log('🔍 DEBUG - Final transformed data:', transformedData);
    console.log('🔍 DEBUG - RIASEC scores:', riasecScores);
    console.log('🔍 DEBUG - OCEAN scores:', ocean);
    console.log('🔍 DEBUG - VIA scores:', viaIs);

    return transformedData;
  };

  const findIncompleteAssessment = (resultsToCheck = assessmentResults) => {
    const { via, riasec, bigFive } = resultsToCheck;

    console.log('🔍 DEBUG - findIncompleteAssessment - Raw resultsToCheck:', resultsToCheck);
    console.log('🔍 DEBUG - findIncompleteAssessment - VIA data:', via);
    console.log('🔍 DEBUG - findIncompleteAssessment - RIASEC data:', riasec);
    console.log('🔍 DEBUG - findIncompleteAssessment - BigFive data:', bigFive);

    // Check if each assessment has meaningful data (not just empty object)
    const isViaComplete = via && Object.keys(via).length > 0 && Object.values(via).some(score => score > 0);
    const isRiasecComplete = riasec && Object.keys(riasec).length > 0 && Object.values(riasec).some(score => score > 0);
    const isBigFiveComplete = bigFive && Object.keys(bigFive).length > 0 && Object.values(bigFive).some(score => score > 0);

    console.log('🔍 DEBUG - findIncompleteAssessment - isViaComplete:', isViaComplete);
    console.log('🔍 DEBUG - findIncompleteAssessment - isRiasecComplete:', isRiasecComplete);
    console.log('🔍 DEBUG - findIncompleteAssessment - isBigFiveComplete:', isBigFiveComplete);

    if (!isViaComplete) {
      console.log('🔍 DEBUG - findIncompleteAssessment - VIA incomplete, returning step 1');
      return { step: 1, name: 'VIA Character Strengths' };
    }
    if (!isRiasecComplete) {
      console.log('🔍 DEBUG - findIncompleteAssessment - RIASEC incomplete, returning step 2');
      return { step: 2, name: 'RIASEC Holland Codes' };
    }
    if (!isBigFiveComplete) {
      console.log('🔍 DEBUG - findIncompleteAssessment - BigFive incomplete, returning step 3');
      return { step: 3, name: 'Big Five Personality' };
    }

    console.log('🔍 DEBUG - findIncompleteAssessment - All assessments complete, returning null');
    return null;
  };

  // Handle final assessment completion and submission
  const handleFinalAssessmentSubmit = async (scores) => {
    console.log('🔍 DEBUG - handleFinalAssessmentSubmit called with scores:', scores);
    console.log('🔍 DEBUG - Current assessment key:', currentAssessment.key);
    console.log('🔍 DEBUG - Previous assessment results:', assessmentResults);

    // First save the final assessment data
    const finalResults = {
      ...assessmentResults,
      [currentAssessment.key]: scores
    };

    console.log('🔍 DEBUG - Final complete assessment results:', finalResults);
    setAssessmentResults(finalResults);

    // Then submit all data
    await handleSubmitAll(finalResults);
  };

  const handleSubmitAll = async (finalAssessmentResults = null) => {
    console.log('🔍 DEBUG - handleSubmitAll called');
    console.log('🔍 DEBUG - isSubmitting:', isSubmitting);
    console.log('🔍 DEBUG - assessmentResults at submit:', assessmentResults);
    console.log('🔍 DEBUG - finalAssessmentResults parameter:', finalAssessmentResults);

    // Prevent double submission
    if (isSubmitting) {
      console.log('🔍 DEBUG - Already submitting, returning early');
      return;
    }

    // Use finalAssessmentResults if provided, otherwise use current assessmentResults
    const resultsToCheck = finalAssessmentResults || assessmentResults;
    console.log('🔍 DEBUG - Results to check for completion:', resultsToCheck);

    // Validate that all assessments are completed
    const incompleteAssessment = findIncompleteAssessment(resultsToCheck);
    console.log('🔍 DEBUG - incompleteAssessment check result:', incompleteAssessment);

    if (incompleteAssessment) {
      console.log('🔍 DEBUG - Found incomplete assessment:', incompleteAssessment);
      setError(`Please complete the ${incompleteAssessment.name} assessment before submitting. Redirecting you to that assessment...`);

      // Redirect to incomplete assessment after a short delay
      setTimeout(() => {
        console.log('🔍 DEBUG - Redirecting to step:', incompleteAssessment.step);
        setCurrentStep(incompleteAssessment.step);
        setError('');
      }, 2000);
      return;
    }

    console.log('🔍 DEBUG - All assessments complete, proceeding with submission...');

    setIsSubmitting(true);
    setError('');

    try {
      // Check token balance before submitting
      const currentBalance = await checkTokenBalance();

      if (currentBalance <= 0) {
        setError('Insufficient token balance. You need at least 1 token to submit an assessment. Please contact support to add more tokens to your account.');
        setIsSubmitting(false);
        return;
      }

      const transformedData = transformScoresForAPI(resultsToCheck);
      console.log('🔍 DEBUG - About to submit to API with data:', transformedData);
      console.log('🔍 DEBUG - API endpoint will be:', '/api/assessment/submit');

      const response = await apiService.submitAssessment(transformedData);
      console.log('🔍 DEBUG - API response received:', response);
      console.log('🔍 DEBUG - API response type:', typeof response);
      console.log('🔍 DEBUG - API response success:', response?.success);
      console.log('🔍 DEBUG - API response data:', response?.data);

      if (response.success) {
        const { jobId } = response.data;
        console.log('🔍 DEBUG - Submission successful, jobId:', jobId);
        console.log('🔍 DEBUG - Navigating to status page...');
        console.log('🔍 DEBUG - Navigation URL will be:', `/assessment/status/${jobId}`);
        // Navigate immediately to status page without loading screen
        navigate(`/assessment/status/${jobId}`, {
          state: { fromSubmission: true }
        });
        console.log('🔍 DEBUG - Navigation called successfully');
      } else {
        console.log('🔍 DEBUG - Submission failed:', response.message);
        setError('Failed to submit assessment: ' + (response.message || 'Unknown error'));
        setIsSubmitting(false);
      }
    } catch (err) {
      console.log('🔍 DEBUG - Error during submission:', err);
      console.log('🔍 DEBUG - Error response:', err.response);
      console.log('🔍 DEBUG - Error response data:', err.response?.data);
      console.log('🔍 DEBUG - Error response status:', err.response?.status);
      console.log('🔍 DEBUG - Error message:', err.message);
      console.log('🔍 DEBUG - Error stack:', err.stack);

      const errorMessage = err.response?.data?.message || err.message || 'Failed to submit assessment';
      console.log('🔍 DEBUG - Final error message:', errorMessage);

      setError(errorMessage);
      setIsSubmitting(false);
    }
  };

  // Remove loading screen - navigate immediately to status page

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {error && (
        <div className="max-w-3xl mx-auto px-4 py-4">
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 shadow-sm">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Assessment Error</h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Debug Mode Indicator */}
      {isDebugMode && import.meta.env.DEV && (
        <div className="max-w-3xl mx-auto px-4 mb-4">
          <div className="bg-orange-100 border border-orange-300 rounded-lg p-3 flex items-center space-x-2">
            <span className="text-orange-600">🔧</span>
            <span className="text-orange-800 font-medium text-sm">
              DEBUG MODE: Assessments are auto-filled for testing
            </span>
          </div>
        </div>
      )}

      <AssessmentForm
        key={currentStep} // Force re-render when step changes to reset state
        assessmentData={currentAssessment.data}
        onSubmit={currentStep === assessments.length ? handleFinalAssessmentSubmit : handleAssessmentComplete}
        onNext={handleNext}
        onPrevious={handlePrevious}
        isLastAssessment={currentStep === assessments.length}
        currentStep={currentStep}
        totalSteps={assessments.length}
        tokenBalance={tokenBalance}
        isCheckingToken={isCheckingToken}
        isDebugMode={isDebugMode}
      />
    </div>
  );
};

export default AssessmentFlow;
