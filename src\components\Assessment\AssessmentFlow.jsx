import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import apiService from '../../services/apiService';
import AssessmentForm from './AssessmentForm';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';

const AssessmentFlow = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const isDebugMode = searchParams.get('debug') === 'true';

  const [currentStep, setCurrentStep] = useState(1);
  const [assessmentResults, setAssessmentResults] = useState({
    via: null,
    riasec: null,
    bigFive: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tokenBalance, setTokenBalance] = useState(null);

  const assessments = [
    { key: 'via', data: viaQuestions, title: 'VIA Character Strengths' },
    { key: 'riasec', data: riasecQuestions, title: 'RIASEC Holland Codes' },
    { key: 'bigFive', data: bigFiveQuestions, title: 'Big Five Personality' }
  ];

  const currentAssessment = assessments[currentStep - 1];

  // Fetch token balance on component mount
  useEffect(() => {
    const fetchTokenBalance = async () => {
      try {
        const response = await apiService.getTokenBalance();
        if (response.success) {
          setTokenBalance(response.data.token_balance);
        }
      } catch (err) {
        setTokenBalance(0);
      }
    };

    fetchTokenBalance();
  }, []);

  // Debug function to auto-fill assessments
  const generateDebugScores = () => {
    return {
      via: {
        wisdomAndKnowledge: 80,
        courage: 75,
        humanity: 85,
        justice: 70,
        temperance: 65,
        transcendence: 75
      },
      riasec: {
        realistic: 75,
        investigative: 85,
        artistic: 60,
        social: 50,
        enterprising: 70,
        conventional: 55
      },
      bigFive: {
        openness: 80,
        conscientiousness: 65,
        extraversion: 55,
        agreeableness: 45,
        neuroticism: 30
      }
    };
  };

  // Auto-fill assessments in debug mode
  useEffect(() => {
    if (isDebugMode && import.meta.env.DEV) {
      const debugScores = generateDebugScores();
      setAssessmentResults(debugScores);
      setCurrentStep(3); // Go to last assessment
    }
  }, [isDebugMode]);

  const handleAssessmentComplete = (scores) => {
    const newResults = {
      ...assessmentResults,
      [currentAssessment.key]: scores
    };

    setAssessmentResults(newResults);

    // Only move to next assessment if not the last one
    if (currentStep < assessments.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleNext = () => {
    if (currentStep < assessments.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  // Transform scores to match the exact API format
  const transformScoresForAPI = (results) => {
    const { via, riasec, bigFive } = results;

    // Transform VIA scores from 6 categories to 24 individual strengths
    const viaIs = {
      // Wisdom and Knowledge (5 strengths)
      creativity: via?.wisdomAndKnowledge || 0,
      curiosity: via?.wisdomAndKnowledge || 0,
      judgment: via?.wisdomAndKnowledge || 0,
      loveOfLearning: via?.wisdomAndKnowledge || 0,
      perspective: via?.wisdomAndKnowledge || 0,
      // Courage (4 strengths)
      bravery: via?.courage || 0,
      perseverance: via?.courage || 0,
      honesty: via?.courage || 0,
      zest: via?.courage || 0,
      // Humanity (3 strengths)
      love: via?.humanity || 0,
      kindness: via?.humanity || 0,
      socialIntelligence: via?.humanity || 0,
      // Justice (3 strengths)
      teamwork: via?.justice || 0,
      fairness: via?.justice || 0,
      leadership: via?.justice || 0,
      // Temperance (4 strengths)
      forgiveness: via?.temperance || 0,
      humility: via?.temperance || 0,
      prudence: via?.temperance || 0,
      selfRegulation: via?.temperance || 0,
      // Transcendence (5 strengths)
      appreciationOfBeauty: via?.transcendence || 0,
      gratitude: via?.transcendence || 0,
      hope: via?.transcendence || 0,
      humor: via?.transcendence || 0,
      spirituality: via?.transcendence || 0
    };

    // Transform RIASEC scores
    const riasecScores = {
      realistic: riasec?.realistic || 0,
      investigative: riasec?.investigative || 0,
      artistic: riasec?.artistic || 0,
      social: riasec?.social || 0,
      enterprising: riasec?.enterprising || 0,
      conventional: riasec?.conventional || 0
    };

    // Transform Big Five scores to OCEAN format
    const ocean = {
      openness: bigFive?.openness || 0,
      conscientiousness: bigFive?.conscientiousness || 0,
      extraversion: bigFive?.extraversion || 0,
      agreeableness: bigFive?.agreeableness || 0,
      neuroticism: bigFive?.neuroticism || 0
    };

    return {
      assessmentName: "AI-Driven Talent Mapping",
      riasec: riasecScores,
      ocean: ocean,
      viaIs: viaIs
    };
  };

  // Validate that all assessments are completed
  const validateAssessments = (results) => {
    const { via, riasec, bigFive } = results;

    if (!via || Object.keys(via).length === 0 || !Object.values(via).some(score => score > 0)) {
      return { isValid: false, missing: 'VIA Character Strengths', step: 1 };
    }
    if (!riasec || Object.keys(riasec).length === 0 || !Object.values(riasec).some(score => score > 0)) {
      return { isValid: false, missing: 'RIASEC Holland Codes', step: 2 };
    }
    if (!bigFive || Object.keys(bigFive).length === 0 || !Object.values(bigFive).some(score => score > 0)) {
      return { isValid: false, missing: 'Big Five Personality', step: 3 };
    }

    return { isValid: true };
  };

  // Handle final assessment completion and submission
  const handleFinalAssessmentSubmit = async (scores) => {
    // First save the final assessment data
    const finalResults = {
      ...assessmentResults,
      [currentAssessment.key]: scores
    };

    setAssessmentResults(finalResults);

    // Then submit all data
    await handleSubmitAll(finalResults);
  };

  const handleSubmitAll = async (finalAssessmentResults = null) => {
    // Prevent double submission
    if (isSubmitting) {
      return;
    }

    const resultsToSubmit = finalAssessmentResults || assessmentResults;

    // Validate that all assessments are completed
    const validation = validateAssessments(resultsToSubmit);
    if (!validation.isValid) {
      setError(`Please complete the ${validation.missing} assessment before submitting.`);
      setTimeout(() => {
        setCurrentStep(validation.step);
        setError('');
      }, 2000);
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Check token balance before submitting
      const response = await apiService.getTokenBalance();
      if (response.success) {
        const balance = response.data.token_balance;
        setTokenBalance(balance);

        if (balance <= 0) {
          setError('Insufficient token balance. You need at least 1 token to submit an assessment.');
          setIsSubmitting(false);
          return;
        }
      }

      const transformedData = transformScoresForAPI(resultsToSubmit);
      const submitResponse = await apiService.submitAssessment(transformedData);

      if (submitResponse.success) {
        const { jobId } = submitResponse.data;
        navigate(`/assessment/status/${jobId}`, {
          state: { fromSubmission: true }
        });
      } else {
        setError('Failed to submit assessment: ' + (submitResponse.message || 'Unknown error'));
        setIsSubmitting(false);
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to submit assessment';
      setError(errorMessage);
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {error && (
        <div className="max-w-3xl mx-auto px-4 py-4">
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 shadow-sm">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Assessment Error</h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Debug Mode Indicator */}
      {isDebugMode && import.meta.env.DEV && (
        <div className="max-w-3xl mx-auto px-4 mb-4">
          <div className="bg-orange-100 border border-orange-300 rounded-lg p-3 flex items-center space-x-2">
            <span className="text-orange-600">🔧</span>
            <span className="text-orange-800 font-medium text-sm">
              DEBUG MODE: Assessments are auto-filled for testing
            </span>
          </div>
        </div>
      )}

      <AssessmentForm
        key={currentStep}
        assessmentData={currentAssessment.data}
        onSubmit={currentStep === assessments.length ? handleFinalAssessmentSubmit : handleAssessmentComplete}
        onNext={handleNext}
        onPrevious={handlePrevious}
        isLastAssessment={currentStep === assessments.length}
        currentStep={currentStep}
        totalSteps={assessments.length}
        tokenBalance={tokenBalance}
        isDebugMode={isDebugMode}
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default AssessmentFlow;
